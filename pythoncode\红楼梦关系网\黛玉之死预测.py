import networkx as nx
import random
from collections import defaultdict
import datetime
import os

# === 第一步：读取节点映射文件 ===
def read_node_mapping(mapping_file):
    """读取节点编号到人物姓名的映射"""
    node_to_name = {}
    with open(mapping_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        for line in lines[1:]:  # 跳过标题行
            if line.strip():
                parts = line.strip().split('\t')
                if len(parts) >= 2:
                    node_id = int(parts[0])
                    name = parts[1]
                    node_to_name[node_id] = name
    return node_to_name

# === 第二步：读取关系网络文件 ===
def read_network(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        n, m = map(int, lines[0].strip().split())
        edges = [tuple(map(int, line.strip().split())) for line in lines[1:]]
    G = nx.DiGraph()
    G.add_nodes_from(range(n))
    for u, v in edges:
        # 随机初始化传播概率，或自行设置权重
        G.add_edge(u, v, prob=random.uniform(0.1, 0.5))
    return G

# === 第二步：模拟IC模型下的传播路径 ===
def simulate_IC(G, seeds, max_steps=10):
    activated = set(seeds)
    newly_activated = set(seeds)
    propagation_path = []

    for step in range(max_steps):
        next_activated = set()
        for u in newly_activated:
            for v in G.successors(u):
                if v not in activated:
                    if random.random() < G[u][v]['prob']:
                        next_activated.add(v)
                        propagation_path.append((u, v))
        if not next_activated:
            break
        activated |= next_activated
        newly_activated = next_activated

    return activated, propagation_path

# === 第三步：分析导致黛玉被激活的关键路径 ===
def reverse_trace(G, target_node, trials=1000):
    count = defaultdict(int)
    path_freq = defaultdict(int)

    for _ in range(trials):
        seeds = [random.randint(0, G.number_of_nodes() - 1)]
        activated, path = simulate_IC(G, seeds)
        if target_node in activated:
            for u, v in path:
                count[u] += 1
                path_freq[(u, v)] += 1

    # 找出影响黛玉最多的人物（以频次衡量）
    sorted_nodes = sorted(count.items(), key=lambda x: x[1], reverse=True)
    sorted_paths = sorted(path_freq.items(), key=lambda x: x[1], reverse=True)
    return sorted_nodes, sorted_paths

# === 第四步：保存分析结果 ===
def save_results(node_contrib, path_contrib, node_to_name, dai_yu_name, dai_yu_id, trials):
    """保存分析结果到文件"""
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    result_dir = "result"
    if not os.path.exists(result_dir):
        os.makedirs(result_dir)

    filename = os.path.join(result_dir, f"黛玉之死预测分析_{timestamp}.txt")

    with open(filename, 'w', encoding='utf-8') as f:
        f.write("=" * 80 + "\n")
        f.write("红楼梦关系网络分析：黛玉之死预测\n")
        f.write("=" * 80 + "\n")
        f.write(f"分析时间：{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"目标人物：{dai_yu_name} (节点编号: {dai_yu_id})\n")
        f.write(f"模拟次数：{trials}\n")
        f.write(f"成功激活次数：{sum(freq for _, freq in node_contrib)}\n")
        f.write(f"发现路径数：{len(path_contrib)}\n\n")

        f.write("💡 影响林黛玉的关键人物（按影响频次排序）：\n")
        f.write("-" * 60 + "\n")
        for i, (node, freq) in enumerate(node_contrib[:20], 1):
            name = node_to_name.get(node, f"未知人物{node}")
            f.write(f"{i:2d}. {name:<15} (节点{node:3d}) - 影响频次: {freq}\n")

        f.write("\n📌 导致林黛玉悲剧的关键路径（频次最高的前20条）：\n")
        f.write("-" * 70 + "\n")
        for i, ((u, v), freq) in enumerate(path_contrib[:20], 1):
            u_name = node_to_name.get(u, f"未知人物{u}")
            v_name = node_to_name.get(v, f"未知人物{v}")
            f.write(f"{i:2d}. {u_name:<15} → {v_name:<15} (频次: {freq})\n")

    return filename

# === 第五步：主程序 ===
def main():
    # 文件路径
    network_file = 'network_data_20250808_191734.txt'  # 网络数据文件
    mapping_file = 'node_mapping_20250808_191734.txt'  # 节点映射文件

    # 读取节点映射
    print("📖 正在读取人物姓名映射...")
    node_to_name = read_node_mapping(mapping_file)
    print(f"✅ 成功读取 {len(node_to_name)} 个人物的映射信息")

    # 读取网络数据
    print("🌐 正在构建关系网络...")
    G = read_network(network_file)
    print(f"✅ 网络构建完成，共有 {G.number_of_nodes()} 个节点，{G.number_of_edges()} 条边")

    # 林黛玉的节点编号（根据映射文件确认）
    dai_yu_id = 127  # 林黛玉的正确节点编号
    dai_yu_name = node_to_name.get(dai_yu_id, f"节点{dai_yu_id}")
    print(f"🎯 目标人物：{dai_yu_name} (节点编号: {dai_yu_id})")

    # 模拟反向传播分析
    trials = 1000
    print(f"\n🔍 正在进行影响路径分析（模拟{trials}次）...")
    node_contrib, path_contrib = reverse_trace(G, dai_yu_id, trials=trials)

    # 计算统计信息
    total_activations = sum(freq for _, freq in node_contrib)
    activation_rate = (total_activations / trials) * 100 if trials > 0 else 0

    print(f"\n💡 影响{dai_yu_name}的关键人物（按影响频次排序）：")
    print("=" * 60)
    for i, (node, freq) in enumerate(node_contrib[:10], 1):
        name = node_to_name.get(node, f"未知人物{node}")
        percentage = (freq / total_activations) * 100 if total_activations > 0 else 0
        print(f"{i:2d}. {name:<15} (节点{node:3d}) - 频次: {freq:3d} ({percentage:5.1f}%)")

    print(f"\n📌 导致{dai_yu_name}悲剧的关键路径（频次最高的前10条）：")
    print("=" * 70)
    for i, ((u, v), freq) in enumerate(path_contrib[:10], 1):
        u_name = node_to_name.get(u, f"未知人物{u}")
        v_name = node_to_name.get(v, f"未知人物{v}")
        print(f"{i:2d}. {u_name:<15} → {v_name:<15} (频次: {freq})")

    # 输出统计信息
    print(f"\n📊 分析统计：")
    print("=" * 40)
    print(f"   • 总模拟次数: {trials}")
    print(f"   • 成功激活{dai_yu_name}的次数: {total_activations}")
    print(f"   • 激活成功率: {activation_rate:.2f}%")
    print(f"   • 发现的影响路径数: {len(path_contrib)}")
    print(f"   • 参与影响的人物数: {len(node_contrib)}")

    # 保存结果
    print(f"\n💾 正在保存分析结果...")
    result_file = save_results(node_contrib, path_contrib, node_to_name, dai_yu_name, dai_yu_id, trials)
    print(f"✅ 分析结果已保存到: {result_file}")

    print(f"\n🎭 分析完成！通过{trials}次模拟，我们发现了影响{dai_yu_name}命运的关键人物和路径。")

if __name__ == '__main__':
    main()
