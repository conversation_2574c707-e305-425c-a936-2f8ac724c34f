# 红楼梦人物关系网络分析

这是一个基于NetworkX的红楼梦人物关系网络分析工具，能够从CSV文件构建人物关系网络并进行深度分析。

## 功能特性

- 从CSV文件直接构建人物关系网络
- 计算多种网络统计特征（度分布、中心性指标、聚类系数等）
- 生成多种中心性指标排名（度中心性、介数中心性、接近中心性、特征向量中心性）
- 智能化网络可视化：
  - 层次化布局展现人物关系结构
  - 根据重要性用不同颜色标识人物
  - 核心人物关系子网络图
  - 度分布统计图表
- 自动保存分析结果到result文件夹
- 生成详细的分析报告
- 导出标准网络数据格式（边列表txt文件）

## 环境要求

- Python 3.7+
- 必要的Python包（见requirements.txt）

## 安装步骤

1. 安装Python依赖：
```bash
pip install -r requirements.txt
```

2. 确保triples.csv文件在当前目录下

## 使用方法

直接运行主程序：
```bash
python honglou_network.py
```

程序将自动执行以下步骤：
1. 连接Neo4j数据库
2. 加载CSV数据并构建网络
3. 计算网络统计特征
4. 生成可视化图表
5. 保存所有结果到result文件夹

## 输出文件

程序会在result文件夹中生成以下文件：

### 统计数据文件
- `network_stats_YYYYMMDD_HHMMSS.json` - 完整的网络统计数据
- `basic_stats_YYYYMMDD_HHMMSS.csv` - 基本网络统计
- `degree_centrality_YYYYMMDD_HHMMSS.csv` - 度中心性排名
- `betweenness_centrality_YYYYMMDD_HHMMSS.csv` - 介数中心性排名
- `closeness_centrality_YYYYMMDD_HHMMSS.csv` - 接近中心性排名
- `eigenvector_centrality_YYYYMMDD_HHMMSS.csv` - 特征向量中心性排名
- `degree_distribution_YYYYMMDD_HHMMSS.csv` - 度分布数据

### 可视化图表
- `network_graph_YYYYMMDD_HHMMSS.png` - 主要连通分量网络关系图（层次化布局）
- `core_network_YYYYMMDD_HHMMSS.png` - 核心人物关系子网络图
- `degree_distribution_YYYYMMDD_HHMMSS.png` - 度分布图

### 分析报告
- `analysis_report_YYYYMMDD_HHMMSS.txt` - 文本格式的分析报告摘要

### 网络数据文件（当前目录）
- `network_data_YYYYMMDD_HHMMSS.txt` - 网络边列表格式数据
- `node_mapping_YYYYMMDD_HHMMSS.txt` - 节点编号与人物姓名对应表

## 网络统计指标说明

- **度中心性**: 衡量节点的直接连接数量
- **介数中心性**: 衡量节点在网络中的桥梁作用
- **接近中心性**: 衡量节点到其他节点的平均距离
- **特征向量中心性**: 考虑邻居节点重要性的中心性指标
- **聚类系数**: 衡量节点邻居之间的连接密度
- **网络密度**: 实际边数与可能边数的比例
- **平均路径长度**: 网络中任意两点间最短路径的平均长度

## 可视化特色

### 网络布局优化
- **层次化布局**：核心人物位于中心，其他人物按与核心人物的距离分层排列
- **颜色编码**：
  - 红色：核心人物（度数最高的5个）
  - 橙色：重要人物（度数≥5）
  - 黄色：次要人物（度数≥3）
  - 浅蓝色：普通人物
- **节点大小**：根据人物的度数（关系数量）动态调整
- **智能标签**：只为重要人物显示姓名标签，避免图像过于拥挤

### 多层次分析
1. **主网络图**：展示最大连通分量的整体结构
2. **核心网络图**：聚焦核心人物及其直接关系
3. **统计图表**：度分布等数据可视化

## 网络数据格式说明

### network_data_*.txt 文件格式
```
388 370          # 第一行：节点数 边数
0 1              # 后续行：边的两个端点编号
0 3
0 4
...
```

### node_mapping_*.txt 文件格式
```
节点编号    人物姓名
0          贾代善
1          贾源
2          娄氏
...
```

这种格式便于导入其他网络分析工具（如Gephi、Cytoscape等）进行进一步分析。

## 注意事项

1. CSV文件格式应为：source,target,relation,label
2. 大型网络的可视化可能需要较长时间
3. 程序会自动处理中文字符显示

## 故障排除

- 如果出现中文显示问题，请确保系统安装了SimHei字体
- 如果内存不足，可以在代码中调整可视化参数
- 确保CSV文件编码为UTF-8格式
